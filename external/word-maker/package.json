{"name": "word-maker", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "NODE_ENV=development nest start --watch", "start:debug": "NODE_ENV=development nest start --debug --watch", "start:prod": "node dist/main", "start:migrate": "npm run migration:run && node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./src/base/ormconfig.ts", "generate-migration": "yarn typeorm -d ./src/base/ormconfig.ts migration:generate", "migration:create": "npm run typeorm -- migration:create ./src/base/database/migrations/$npm_config_name", "update-database": "yarn typeorm -d ./src/base/ormconfig.ts migration:run", "migration:revert": "npm run typeorm -- -d ./src/base/ormconfig.ts migration:revert"}, "dependencies": {"@liaoliaots/nestjs-redis": "^10.0.0", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^10.1.0", "@nestjs/swagger": "^8.1.1", "@nestjs/typeorm": "^11.0.0", "@types/got": "^9.6.12", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "exceljs": "^4.4.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "nestjs-soap": "^3.0.4", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.15.1", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "strong-soap": "^4.1.11", "typeorm": "^0.3.22", "zarinpal-checkout": "^0.3.0"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^10.4.16", "@types/express": "^5.0.1", "@types/jest": "29.5.14", "@types/node": "^22.14.1", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "fork-ts-checker-webpack-plugin": "^9.1.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}