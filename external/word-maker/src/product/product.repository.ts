import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { products } from '../base/database/entities/product.entity';

@Injectable()
export class ProductRepository {
  constructor(
    @InjectRepository(products)
    private readonly repository: Repository<products>,
  ) {}
  async get(): Promise<products[]> {
    const result = await this.repository.find();
    return result;
  }
  async getById(id: number): Promise<products> {
    const result = await this.repository.findOneBy({ id });
    return result;
  }
  async getBySibAppId(sib_app_id: string): Promise<products> {
    const result = await this.repository.findOneBy({ sib_app_id });
    return result;
  }
}
