import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { discounts } from '../base/database/entities/discounts.entity';
import {
  DiscountSaveDto,
  DiscountSearchDto,
  UpdateDiscountInputDto,
} from './discount.dto';

@Injectable()
export class DiscountRepository {
  constructor(
    @InjectRepository(discounts)
    private readonly repository: Repository<discounts>,
  ) {}
  async getByCode(code: string): Promise<discounts> {
    return await this.repository.findOneBy({ code });
  }
  async get(discountSearchDto: DiscountSearchDto): Promise<discounts[]> {
    const where: any = {};

    if (
      discountSearchDto.state !== null &&
      discountSearchDto.state !== undefined
    ) {
      where.state = discountSearchDto.state;
    }
    if (
      discountSearchDto.type !== null &&
      discountSearchDto.type !== undefined
    ) {
      where.type = discountSearchDto.type;
    }
    if (
      discountSearchDto.referral_id !== null &&
      discountSearchDto.referral_id !== undefined
    ) {
      where.referral_id = discountSearchDto.referral_id;
    }
    if (
      discountSearchDto.remain_count !== null &&
      discountSearchDto.remain_count !== undefined
    ) {
      where.remain_count = discountSearchDto.remain_count;
    }
    if (
      discountSearchDto.description !== null &&
      discountSearchDto.description !== undefined &&
      discountSearchDto.description !== ''
    ) {
      where.description = discountSearchDto.description;
    }
    if (
      discountSearchDto.code !== null &&
      discountSearchDto.code !== undefined &&
      discountSearchDto.code !== ''
    ) {
      where.code = discountSearchDto.code;
    }
    const result = await this.repository.find({ where });
    return result;
  }

  async save(dto: DiscountSaveDto[]): Promise<discounts[]> {
    const result = await this.repository.save(dto);
    return result;
  }
  async updateStatus(dto: UpdateDiscountInputDto) {
    const result = await this.repository.update(dto.id, {
      state: dto.state,
    });
    return result;
  }
}
