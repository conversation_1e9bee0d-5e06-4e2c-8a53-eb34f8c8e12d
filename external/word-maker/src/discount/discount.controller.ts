import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  UseInterceptors,
  UsePipes,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';
import {
  ConsumeDiscountInputDto,
  ConsumeDiscountOutputDto,
  DiscountInputDto,
  DiscountOutputDto,
  DiscountSearchDto,
  UpdateDiscountInputDto,
} from './discount.dto';
import { DiscountService } from './discount.service';
import { DateTransformPipe } from '../base/utils/pipes/date-trasform';
import { AuthGuard } from '../auth/auth.guard';
import { AdminGuard } from '../auth/admin.guard';
import { TransformDateInterceptor } from '../base/utils/interceptors/date-interceptors';
import { ExpireDateValidationPipe } from '../base/utils/pipes/expire_date-validation';
import { HttpResponseDto } from '../base/dto/general.dto';

@Controller('discounts')
@ApiTags('discounts')
@ApiBearerAuth()
export class DiscountController {
  constructor(
    private discountService: DiscountService,
    private readonly dateTransformPipe: DateTransformPipe,
  ) {}

  @Get('/all')
  @UseGuards(AdminGuard)
  @ApiResponse({
    status: 200,
    description: 'لیست کد های تخفیف',
    type: DiscountOutputDto,
  })
  @UseInterceptors(MapInterceptor(DiscountOutputDto))
  async discounts(
    @Query() discountSearchDto: DiscountSearchDto,
  ): Promise<DiscountOutputDto[]> {
    return await this.discountService.getDiscount(discountSearchDto);
  }

  @Post('/create')
  @UseGuards(AdminGuard)
  @UseInterceptors(MapInterceptor(DiscountOutputDto))
  @UseInterceptors(TransformDateInterceptor)
  @UsePipes(ExpireDateValidationPipe)
  async createDiscount(
    @Body() createDiscountDto: DiscountInputDto,
  ): Promise<DiscountOutputDto[]> {
    createDiscountDto.expire_date = this.dateTransformPipe.transform(
      createDiscountDto.expire_date,
    );
    return this.discountService.createDiscount(createDiscountDto);
  }

  @Post('/change-status')
  @UseGuards(AdminGuard)
  @UseInterceptors(MapInterceptor(HttpResponseDto))
  async updateDiscountStatus(
    @Body() updateDiscountDto: UpdateDiscountInputDto,
  ): Promise<HttpResponseDto> {
    return this.discountService.updateDiscountStatus(updateDiscountDto);
  }

  @Post('/consume')
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'استفاده از کد تخفیف',
    type: ConsumeDiscountOutputDto,
  })
  async consumeDiscount(
    @Body() consumeDiscountDto: ConsumeDiscountInputDto,
  ): Promise<ConsumeDiscountOutputDto> {
    return this.discountService.consumeDiscount(consumeDiscountDto);
  }
}
