import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
} from 'class-validator';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import {
  discounts,
  DiscountStateEnum,
  DiscountTypeEnum,
} from '../base/database/entities/discounts.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';

export class DiscountSearchDto {
  @IsEnum(DiscountStateEnum, {
    message:
      'وضعیت کد تخفیف باید یکی از مقادیر ACTIVE یا DEACTIVE یا USED یا UNUSED باشد',
  })
  @ApiPropertyOptional({
    enum: DiscountStateEnum,
    example: 'ACTIVE',
    description: 'وضعیت کد تخفیف',
  })
  @IsOptional()
  state?: DiscountStateEnum;

  @IsEnum(DiscountTypeEnum)
  @ApiPropertyOptional({
    enum: DiscountTypeEnum,
    example: 'simple',
    description: 'نوع کد تخفیف',
  })
  @IsOptional()
  @Mapped()
  type?: DiscountTypeEnum;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Mapped()
  referral_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Mapped()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Mapped()
  code?: string;
}

export class DiscountOutputDto implements MappedWithEntity<discounts> {
  @ApiProperty()
  @Mapped()
  id: number;
  @ApiProperty()
  @Mapped()
  code: string;
  @ApiProperty({ description: 'درصد تخفیف' })
  @Mapped()
  discount_percent: number;
  @ApiProperty({ description: 'تاریخ انقضا' })
  @Mapped()
  expire_date?: Date;
  @ApiProperty({
    example: 'active',
    description: 'وضعیت کد تخفیف ',
    enum: DiscountStateEnum,
  })
  @Mapped()
  state?: DiscountStateEnum;
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @Mapped()
  type?: DiscountTypeEnum;
  @ApiProperty({ description: 'شناسه ارجاع' })
  @Mapped()
  referral_id?: number;
  @ApiProperty({ description: 'توضیحات' })
  @Mapped()
  description?: string;
}

export class DiscountInputDto {
  @ApiProperty({ example: 'YYYY-MM-DD', description: 'تاریخ انقضای کد تخفیف' })
  @IsOptional()
  @IsDateString()
  expire_date: string;

  @IsEnum(DiscountStateEnum)
  @ApiProperty({ example: 'ACTIVE', description: 'وضعیت کد تخفیف ' })
  @IsOptional()
  state?: DiscountStateEnum;

  @ApiProperty()
  @IsNumber()
  discount_percent: number;

  @ApiProperty()
  @IsNumber()
  count: number;

  @IsEnum(DiscountTypeEnum)
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @IsOptional()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'شناسه ارجاع' })
  @IsNumber()
  @IsOptional()
  referral_id?: number;

  @ApiProperty({ description: 'توضیحات' })
  @IsString()
  @IsOptional()
  description?: string;
}

export class DiscountSaveDto {
  @ApiProperty()
  @IsOptional()
  expire_date?: string;

  @ApiProperty({ example: '10', description: 'درصد تخفیف' })
  @IsNumber()
  discount_percent: number;

  @IsEnum(DiscountStateEnum)
  @ApiProperty({ example: 'active', description: 'وضعیت' })
  @IsOptional()
  state?: DiscountStateEnum;

  @IsEnum(DiscountTypeEnum)
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @IsOptional()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'شناسه ارجاع' })
  @IsNumber()
  @IsOptional()
  referral_id?: number;

  @ApiProperty({ description: 'توضیحات' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'code', description: 'کد تخفیف' })
  @IsString()
  code: string;
}

export class UpdateDiscountInputDto {
  @ApiProperty({ example: '1', description: 'id' })
  @IsNumber()
  id: number;

  @IsEnum(DiscountStateEnum)
  @ApiProperty({ example: 'ACTIVE', description: 'وضعیت' })
  @IsOptional()
  state?: DiscountStateEnum;
}

export class ConsumeDiscountInputDto {
  @ApiProperty({ example: 'ABCD', description: 'کد تخفیف' })
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class ConsumeDiscountOutputDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  discount?: DiscountOutputDto;
}
