import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MapperService } from '../base/utils/mapper/mapper.service';
import { DiscountController } from './discount.controller';
import { DiscountRepository } from './discount.repository';
import { DiscountService } from './discount.service';
import { discounts } from '../base/database/entities/discounts.entity';
import { TokenService } from '../token/token.service';
import { DateTransformPipe } from '../base/utils/pipes/date-trasform';
@Module({
  imports: [TypeOrmModule.forFeature([discounts])],
  providers: [
    DiscountService,
    DiscountRepository,
    MapperService,
    TokenService,
    DateTransformPipe,
  ],
  controllers: [DiscountController],
  exports: [DiscountService],
})
export class DiscountModule {}
