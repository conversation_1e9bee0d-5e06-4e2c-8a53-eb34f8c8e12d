import { Injectable } from '@nestjs/common';
import { DiscountRepository } from './discount.repository';
import {
  DiscountInputDto,
  DiscountSaveDto,
  DiscountSearchDto,
  UpdateDiscountInputDto,
} from './discount.dto';
import { discounts } from '../base/database/entities/discounts.entity';
import { convertToHttpResponseDto } from '../base/utils/convert-info';
import { HttpResponseDto } from '../base/dto/general.dto';
import { DiscountStateEnum } from '../base/enums/purchase-state.enum';
@Injectable()
export class DiscountService {
  constructor(private readonly discountRepository: DiscountRepository) {}

  async getDiscount(discountSearchDto: DiscountSearchDto) {
    return await this.discountRepository.get(discountSearchDto);
  }

  async createDiscount(
    createDiscountDto: DiscountInputDto,
  ): Promise<discounts[]> {
    const discounts: Array<DiscountSaveDto> = [];
    for (let i = 0; i < createDiscountDto.count; i++) {
      discounts.push(<DiscountSaveDto>{
        ...createDiscountDto,
        code: this.generateDiscountCode(),
        type: createDiscountDto.type,
        referral_id: createDiscountDto.referral_id,
        description: createDiscountDto.description,
      });
    }
    return await this.discountRepository.save(discounts);
  }

  async updateDiscountStatus(
    updateDiscountDto: UpdateDiscountInputDto,
  ): Promise<HttpResponseDto> {
    return convertToHttpResponseDto(
      await this.discountRepository.updateStatus(updateDiscountDto),
    );
  }

  private generateDiscountCode(length = 4): string {
    const characters = 'ABCDEFGHIJKMNPQRSTUVWXYZ23456789';
    return Array.from({ length }, () =>
      characters.charAt(Math.floor(Math.random() * characters.length)),
    ).join('');
  }

  async getDiscountByCode(code: string) {
    return await this.discountRepository.getByCode(code);
  }
  async expireDiscount(id: number) {
    return await this.discountRepository.updateStatus({
      id,
      state: DiscountStateEnum.USED,
    });
  }
}
