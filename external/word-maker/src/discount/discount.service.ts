import { Injectable } from '@nestjs/common';
import { DiscountRepository } from './discount.repository';
import {
  ConsumeDiscountInputDto,
  ConsumeDiscountOutputDto,
  DiscountInputDto,
  DiscountSaveDto,
  DiscountSearchDto,
  UpdateDiscountInputDto,
} from './discount.dto';
import {
  discounts,
  DiscountStateEnum,
} from '../base/database/entities/discounts.entity';
import { convertToHttpResponseDto } from '../base/utils/convert-info';
import { HttpResponseDto } from '../base/dto/general.dto';
@Injectable()
export class DiscountService {
  constructor(private readonly discountRepository: DiscountRepository) {}

  async getDiscount(discountSearchDto: DiscountSearchDto) {
    return await this.discountRepository.get(discountSearchDto);
  }

  async createDiscount(
    createDiscountDto: DiscountInputDto,
  ): Promise<discounts[]> {
    const discounts: Array<DiscountSaveDto> = [];
    for (let i = 0; i < createDiscountDto.count; i++) {
      discounts.push(<DiscountSaveDto>{
        ...createDiscountDto,
        code: this.generateDiscountCode(),
        type: createDiscountDto.type,
        referral_id: createDiscountDto.referral_id,
        description: createDiscountDto.description,
      });
    }
    return await this.discountRepository.save(discounts);
  }

  async updateDiscountStatus(
    updateDiscountDto: UpdateDiscountInputDto,
  ): Promise<HttpResponseDto> {
    return convertToHttpResponseDto(
      await this.discountRepository.updateStatus(updateDiscountDto),
    );
  }

  private generateDiscountCode(length = 4): string {
    const characters = 'ABCDEFGHIJKMNPQRSTUVWXYZ23456789';
    return Array.from({ length }, () =>
      characters.charAt(Math.floor(Math.random() * characters.length)),
    ).join('');
  }

  async getDiscountByCode(code: string) {
    return await this.discountRepository.getByCode(code);
  }
  async expireDiscount(id: number) {
    return await this.discountRepository.updateStatus({
      id,
      state: DiscountStateEnum.USED,
    });
  }

  async consumeDiscount(
    consumeDiscountDto: ConsumeDiscountInputDto,
  ): Promise<ConsumeDiscountOutputDto> {
    const discount = await this.discountRepository.getByCode(
      consumeDiscountDto.code,
    );

    if (!discount) {
      return {
        success: false,
        message: 'کد تخفیف یافت نشد',
      };
    }

    if (discount.state !== DiscountStateEnum.ACTIVE) {
      return {
        success: false,
        message: 'کد تخفیف غیرفعال یا استفاده شده است',
      };
    }

    // Check if discount is expired
    if (discount.expire_date && new Date() > discount.expire_date) {
      return {
        success: false,
        message: 'کد تخفیف منقضی شده است',
      };
    }

    // Mark discount as used
    await this.discountRepository.updateStatus({
      id: discount.id,
      state: DiscountStateEnum.USED,
    });

    return {
      success: true,
      message: 'کد تخفیف با موفقیت استفاده شد',
      discount,
    };
  }
}
