import { Injectable } from '@nestjs/common';
import { DiscountRepository } from './discount.repository';
import {
  CheckDiscountInputDto,
  CheckDiscountOutputDto,
  DiscountInputDto,
  DiscountSaveDto,
  DiscountSearchDto,
  UpdateDiscountInputDto,
} from './discount.dto';
import {
  discounts,
  DiscountStateEnum,
} from '../base/database/entities/discounts.entity';
import { convertToHttpResponseDto } from '../base/utils/convert-info';
import { HttpResponseDto } from '../base/dto/general.dto';
@Injectable()
export class DiscountService {
  constructor(private readonly discountRepository: DiscountRepository) {}

  async getDiscount(discountSearchDto: DiscountSearchDto) {
    return await this.discountRepository.get(discountSearchDto);
  }

  async generateDiscounts(
    createDiscountDto: DiscountInputDto,
  ): Promise<discounts[]> {
    const uniqueCodes = await this.generateBulkUniqueCodes(
      createDiscountDto.count,
    );
    const discounts: Array<DiscountSaveDto> = [];

    for (let i = 0; i < createDiscountDto.count; i++) {
      discounts.push(<DiscountSaveDto>{
        ...createDiscountDto,
        code: uniqueCodes[i],
        type: createDiscountDto.type,
        referral_id: createDiscountDto.referral_id,
        description: createDiscountDto.description,
      });
    }
    return await this.discountRepository.save(discounts);
  }

  async updateDiscountStatus(
    updateDiscountDto: UpdateDiscountInputDto,
  ): Promise<HttpResponseDto> {
    return convertToHttpResponseDto(
      await this.discountRepository.updateStatus(updateDiscountDto),
    );
  }

  private generateDiscountCode(length = 6): string {
    const characters = 'ABCDEFGHIJKMNPQRSTUVWXYZ23456789';
    return Array.from({ length }, () =>
      characters.charAt(Math.floor(Math.random() * characters.length)),
    ).join('');
  }

  private async generateBulkUniqueCodes(count: number): Promise<string[]> {
    const uniqueCodes: string[] = [];
    const generatedCodes = new Set<string>();
    let totalAttempts = 0;
    const maxTotalAttempts = count * 100; // Allow more attempts for bulk generation

    while (uniqueCodes.length < count && totalAttempts < maxTotalAttempts) {
      const code = this.generateDiscountCode();

      // Check if we already generated this code in this batch
      if (generatedCodes.has(code)) {
        totalAttempts++;
        continue;
      }

      // Check if ACTIVE code exists in database (most important check)
      const existingDiscount = await this.discountRepository.getByCode(code);
      if (
        !existingDiscount ||
        existingDiscount.state !== DiscountStateEnum.ACTIVE
      ) {
        // Code is unique or exists but not ACTIVE, so we can use it
        uniqueCodes.push(code);
        generatedCodes.add(code);
      }

      totalAttempts++;
    }

    if (uniqueCodes.length < count) {
      throw new Error(
        `Unable to generate ${count} unique discount codes. Only generated ${uniqueCodes.length} codes after ${totalAttempts} attempts. Consider increasing code length.`,
      );
    }

    return uniqueCodes;
  }

  async getDiscountByCode(code: string) {
    return await this.discountRepository.getByCode(code);
  }

  async expireDiscount(id: number) {
    return await this.discountRepository.updateStatus({
      id,
      state: DiscountStateEnum.USED,
    });
  }

  async checkDiscountCodeActive(
    checkDiscountDto: CheckDiscountInputDto,
  ): Promise<CheckDiscountOutputDto> {
    const discount = await this.discountRepository.getByCode(
      checkDiscountDto.code,
    );

    if (!discount) {
      return {
        success: false,
        message: 'کد تخفیف یافت نشد',
      };
    }

    if (discount.state !== DiscountStateEnum.ACTIVE) {
      return {
        success: false,
        message: 'کد تخفیف غیرفعال یا استفاده شده است',
      };
    }

    // Check if discount is expired
    if (discount.expire_date && new Date() > discount.expire_date) {
      return {
        success: false,
        message: 'کد تخفیف منقضی شده است',
      };
    }

    // Return validation result without consuming the discount
    return {
      success: true,
      message: 'کد تخفیف معتبر و قابل استفاده است',
      discount,
    };
  }
}
