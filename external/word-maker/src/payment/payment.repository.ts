import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { payments } from '../base/database/entities/payments.entity';
import { PaymentSaveInputDto } from './payment.dto';

@Injectable()
export class PaymentsRepository {
  constructor(
    @InjectRepository(payments)
    private readonly repository: Repository<payments>,
  ) {}
  async save(payment: PaymentSaveInputDto): Promise<payments> {
    const result = await this.repository.save(payment);
    return result;
  }
  async update(payment: payments): Promise<payments> {
    const result = await this.repository.save(payment);
    return result;
  }
  async findOneByOrderId(order_id: string): Promise<payments> {
    const result = await this.repository.findOneBy({ order_id });
    return result;
  }
  async findOneById(id: number, user_id: number): Promise<payments> {
    return this.repository.findOne({
      where: { id, user_id },
      relations: { discount: true },
    });
  }
}
