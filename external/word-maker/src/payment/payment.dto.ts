import { ApiProperty } from '@nestjs/swagger';
import {
  PaymentStateEnum,
  PaymentTypeEnum,
} from '../base/enums/purchase-state.enum';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { payments } from '../base/database/entities/payments.entity';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { DiscountOutputDto } from '../discount/discount.dto';
import { Type } from 'class-transformer';

export class PaymentOutputDto implements MappedWithEntity<payments> {
  @ApiProperty()
  @Mapped()
  id: number;
  @ApiProperty()
  @Mapped()
  price?: number;
  @ApiProperty()
  @Mapped()
  final_price?: number;
  @ApiProperty()
  @Mapped()
  state?: PaymentStateEnum;
  @ApiProperty()
  @Mapped()
  order_id?: string;

  @Mapped(() => DiscountOutputDto)
  @Type(() => DiscountOutputDto)
  @ApiProperty()
  discount: DiscountOutputDto;
}
export class PaymentSaveInputDto {
  price: number;
  product_id: number;
  final_price: number;
  user_id: number;
  order_id: string;
  discount_id: number;
  state: PaymentStateEnum;
}
export class CreatePaymentDto {
  @ApiProperty({
    example: 1,
    description: 'شناسه محصول',
    required: true,
  })
  @IsNotEmpty()
  product_id: number;

  @ApiProperty({
    example: 'ABCD12',
    description: 'کد تخفیف ',
    required: false,
  })
  @IsOptional()
  discount_code?: string;

  @ApiProperty({
    example: PaymentTypeEnum.ZARINPAL,
    description: 'نوع درگاه',
    required: true,
  })
  @IsEnum(PaymentTypeEnum, {
    message: 'an unknown value was passed to the validate function',
  })
  @IsNotEmpty()
  payment_type: PaymentTypeEnum;

  @ApiProperty({
    example: 'http://localhost:3002/payment/verify',
    description: 'آدرس بازگشت پرداخت',
    required: true,
  })
  @IsNotEmpty()
  callback_url: string;
}

export class CreatePaymentOutputDto {
  @ApiProperty()
  @Mapped()
  authority: string;
  @ApiProperty({
    description: 'وضعیت خرید کاربر',
  })
  @Mapped()
  url: string;

  @ApiProperty()
  @Mapped()
  id: number;
}
