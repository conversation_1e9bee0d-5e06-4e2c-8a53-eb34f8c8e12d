import { Module } from '@nestjs/common';
import { PaymentService } from './payment.service';
import { PaymentController } from './payment.controller';
import { LogModule } from '../base/logger/log.module';
import { PaymentsRepository } from './payment.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { payments } from '../base/database/entities/payments.entity';
import { ProductModule } from '../product/product.module';
import { DiscountModule } from '../discount/discount.module';
import { TokenService } from '../token/token.service';
import { purchases } from '../base/database/entities/purchase.entity';
import { PurchasesRepository } from '../purchase/purchase.repository';
import { MapperService } from '../base/utils/mapper/mapper.service';
@Module({
  imports: [
    LogModule,
    TypeOrmModule.forFeature([payments]),
    TypeOrmModule.forFeature([purchases]),
    ProductModule,
    DiscountModule,
  ],
  providers: [
    PaymentService,
    PaymentsRepository,
    TokenService,
    MapperService,
    PurchasesRepository,
  ],
  controllers: [PaymentController],
  exports: [PaymentService],
})
export class PaymentModule {}
