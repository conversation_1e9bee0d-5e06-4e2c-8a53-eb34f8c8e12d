import { Injectable } from '@nestjs/common';
import { PaymentsRepository } from './payment.repository';
import { LogService } from '../base/logger/log.service';
import * as ZarinpalCheckout from 'zarinpal-checkout';
import {
  PaymentStateEnum,
  PurchaseStateEnum,
} from '../base/enums/purchase-state.enum';
import {
  CreatePaymentDto,
  CreatePaymentOutputDto,
  PaymentSaveInputDto,
} from './payment.dto';
import { ProductService } from '../product/product.service';
import { DiscountService } from '../discount/discount.service';
import { PurchasesRepository } from '../purchase/purchase.repository';
import { DiscountStateEnum } from 'src/base/database/entities/discounts.entity';
@Injectable()
export class PaymentService {
  private zarinpal;

  constructor(
    private readonly paymentsRepository: PaymentsRepository,
    private readonly purchasesRepository: PurchasesRepository,
    private readonly logService: LogService,
    private readonly productService: ProductService,
    private readonly discountService: DiscountService,
  ) {
    this.zarinpal = ZarinpalCheckout.create(
      process.env.ZARIN_MERCHANT_ID,
      false,
    ); // true برای sandbox است
  }

  async createPayment(
    createPaymentDto: CreatePaymentDto,
  ): Promise<CreatePaymentOutputDto> {
    try {
      const { product_id, discount_code } = createPaymentDto;
      const product = await this.getProduct(product_id);
      let final_price = product.price;
      let discount_id;
      if (discount_code) {
        const discount = await this.getDiscount(discount_code);
        discount_id = discount.id;
        const discountAmount = (final_price * discount.discount_percent) / 100;
        final_price = final_price - discountAmount;
      }
      const result = await this.zarinpal.PaymentRequest({
        Amount: final_price / 10,
        CallbackURL: createPaymentDto.callback_url,
        Description: 'خرید فایل APK',
      });
      const { authority, status } = result;
      if (status === 100) {
        // ذخیره اطلاعات پرداخت در دیتابیس
        const payment = Object.assign(new PaymentSaveInputDto(), {
          price: product.price,
          final_price,
          product_id,
          discount_id: discount_id ?? null,
          order_id: authority,
          user_id: 0,
          state: PaymentStateEnum.SEND_TO_BANK,
        });
        // const { id } = await this.paymentsRepository.save(payment);

        return {
          authority,
          url: `https://www.zarinpal.com/pg/StartPay/${authority}`,
          id: 0,
        };
      } else {
        this.logService.createLog(
          'Failed to create payment-zarrinPal.',
          result.data,
        );
        throw new Error('Failed to create payment.');
      }
    } catch (e) {
      this.logService.createLog(`Failed to create payment. ${e.message}`, e);
      throw new Error(`Failed to create payment. ${e.message}`);
    }
  }
  private async getProduct(id: number) {
    const product = await this.productService.getProductById(id);
    if (!product) {
      this.logService.createLog('Product not found.', id.toString());
      throw new Error('Product not found.');
    }
    return product;
  }
  private async getDiscount(code: string) {
    const discount = await this.discountService.getDiscountByCode(code);
    if (!discount || discount.state != DiscountStateEnum.ACTIVE) {
      this.logService.createLog('Discount not found.', code.toString());
      throw new Error('Discount not found.');
    } else return discount;
  }
  async verifyPayment(
    authority: string,
    status: string,
  ): Promise<{ success: boolean; message: string }> {
    const payment = await this.paymentsRepository.findOneByOrderId(authority);
    if (!payment) {
      throw new Error('Payment not found.');
    }
    if (status !== 'OK') {
      payment.state = PaymentStateEnum.UNSUCCESS;
      await this.paymentsRepository.update(payment);
      this.logService.createLog(
        'Payment not successful.',
        `${authority} - ${status}`,
      );
      return { success: false, message: 'پرداخت توسط کاربر لغو شد.' };
    }

    try {
      const result = await this.zarinpal.PaymentVerification({
        Amount: payment.final_price / 10,
        Authority: authority,
      });

      if (result.status === 100) {
        payment.state = PaymentStateEnum.SUCCESS;
        await this.paymentsRepository.update(payment);
        await this.savePurchase(
          payment.user_id,
          payment.product_id,
          payment.id,
        );
        if (payment.discount_id)
          await this.expireDiscountCode(payment.discount_id);
        return { success: true, message: 'پرداخت با موفقیت انجام شد.' };
      } else {
        payment.state = PaymentStateEnum.UNSUCCESS;
        await this.paymentsRepository.update(payment);
        return { success: false, message: 'پرداخت موفق نبود.' };
      }
    } catch (e) {
      this.logService.createLog('Payment verification failed.', e);
      throw new Error('Failed to verify payment.');
    }
  }
  private async expireDiscountCode(discount_id: number) {
    await this.discountService.expireDiscount(discount_id);
  }
  private async savePurchase(
    user_id: number,
    product_id: number,
    payment_id: number,
  ) {
    await this.purchasesRepository.save({
      user_id,
      product_id,
      time: new Date(),
      state: PurchaseStateEnum.PURCHASED,
      payment_id,
    });
  }

  async getPayment(id: number, user_id: number) {
    const purchases = await this.paymentsRepository.findOneById(id, user_id);
    return purchases;
  }
}
