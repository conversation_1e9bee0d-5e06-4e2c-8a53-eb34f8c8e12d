import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ApiBearerAuth, ApiBody, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  CreatePaymentDto,
  CreatePaymentOutputDto,
  PaymentOutputDto,
} from './payment.dto';
import { AuthGuard } from '../auth/auth.guard';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';

@Controller('payment')
@ApiTags('Payment')
export class PaymentController {
  constructor(private paymentService: PaymentService) {}

  @Post()
  @ApiBody({ type: CreatePaymentDto })
  @ApiResponse({
    status: 200,
    description: 'ثبت پرداخت کاربر',
    type: CreatePaymentOutputDto,
  })
  @UseInterceptors(MapInterceptor(CreatePaymentOutputDto))
  async createPayment(
    @Body() createPaymentDto: CreatePaymentDto,
    @Request() req,
  ) {
    return await this.paymentService.createPayment(createPaymentDto);
  }

  @Get('verify')
  @ApiResponse({
    status: 200,
    description: 'وریفای پرداخت کاربر',
  })
  async verifyPayment(
    @Query('Authority') authority: string,
    @Query('Status') status: string,
  ) {
    return this.paymentService.verifyPayment(authority, status);
  }

  @Get('/:id')
  @UseGuards(AuthGuard)
  @ApiBearerAuth()
  @ApiResponse({
    status: 200,
    description: 'اطلاعات پرداخت کاربر',
    type: PaymentOutputDto,
  })
  @UseInterceptors(MapInterceptor(PaymentOutputDto))
  async getCohortById(
    @Param('id', ParseIntPipe) id: number,
    @Request() req,
  ): Promise<PaymentOutputDto> {
    return this.paymentService.getPayment(id, req.user.id);
  }
}
