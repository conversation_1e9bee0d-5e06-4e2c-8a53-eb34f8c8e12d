export enum PurchaseStateEnum {
  PURCHASED = 'PURCHASED',
  REFUNDED = 'REFUNDED',
}

export enum DiscountStateEnum {
  ACTIVE = 'ACTIVE',
  DEACTIVE = 'DEACTIVE',
  USED = 'USED',
}

export enum PaymentStateEnum {
  NEW = 'NEW',
  SEND_TO_BANK = 'SEND_TO_BANK',
  SUCCESS = 'SUCCESS',
  UNSUCCESS = 'UNSUCCESS',
}

export enum PaymentTypeEnum {
  ZARINPAL = 'ZARINPAL',
}

export enum PaymentFromEnum {
  BAZAR = 'BAZAR',
  SIBAPP = 'SIBAPP',
  ZARINPAL = 'ZARINPAL',
}
