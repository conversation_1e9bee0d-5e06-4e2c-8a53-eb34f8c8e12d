import { AfterLoad, Column, Entity, OneToMany, Index } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { CreateDateUTCColumn } from '../../decorators/date.decorator';
import { payments } from './payments.entity';

export enum DiscountTypeEnum {
  SIMPLE = 'simple',
  REFERRAL = 'referral',
}

export enum DiscountStateEnum {
  ACTIVE = 'ACTIVE',
  DEACTIVE = 'DEACTIVE',
  USED = 'USED',
}

@Entity()
@Index('IDX_DISCOUNT_CODE', ['code'], { unique: true })
export class discounts extends AbstractEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  discount_percent: number;

  @CreateDateUTCColumn()
  expire_date: Date;

  @Column({
    type: 'enum',
    enum: DiscountStateEnum,
    default: DiscountStateEnum.ACTIVE,
  })
  state: DiscountStateEnum;

  @Column({
    type: 'enum',
    enum: DiscountTypeEnum,
    default: DiscountTypeEnum.SIMPLE,
  })
  type: DiscountTypeEnum;

  @Column({ nullable: true, default: null })
  referral_id: number;

  @Column({ nullable: true, default: null })
  description: string;

  @OneToMany(() => payments, (payment) => payment.discount)
  payments: payments[];

  @AfterLoad()
  convertExpireDateToUTC() {
    if (this.expire_date) {
      this.expire_date = new Date(
        this.expire_date.getTime() -
          this.expire_date.getTimezoneOffset() * 60000,
      );
    }
  }
}
