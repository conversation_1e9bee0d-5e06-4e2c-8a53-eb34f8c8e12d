import { AfterLoad, Column, <PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { CreateDateUTCColumn } from '../../decorators/date.decorator';
import { DiscountStateEnum } from '../../enums/purchase-state.enum';
import { payments } from './payments.entity';

@Entity()
export class discounts extends AbstractEntity {
  @Column()
  code: string;

  @Column()
  discount_percent: number;

  @CreateDateUTCColumn()
  expire_date: Date;

  @Column()
  state: DiscountStateEnum;

  @Column()
  company_name: string;

  @OneToMany(() => payments, (payment) => payment.discount)
  payments: payments[];

  @AfterLoad()
  convertExpireDateToUTC() {
    if (this.expire_date) {
      this.expire_date = new Date(
        this.expire_date.getTime() -
          this.expire_date.getTimezoneOffset() * 60000,
      );
    }
  }
}
