import { AfterLoad, Column, <PERSON><PERSON>ty, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { CreateDateUTCColumn } from '../../decorators/date.decorator';
import { DiscountStateEnum } from '../../enums/purchase-state.enum';
import { payments } from './payments.entity';

export enum DiscountTypeEnum {
  SIMPLE = 'simple',
  REFERRAL = 'referral',
}

@Entity()
export class discounts extends AbstractEntity {
  @Column()
  code: string;

  @Column()
  discount_percent: number;

  @CreateDateUTCColumn()
  expire_date: Date;

  @Column()
  state: DiscountStateEnum;

  @Column({
    type: 'enum',
    enum: DiscountTypeEnum,
    default: DiscountTypeEnum.SIMPLE,
  })
  type: DiscountTypeEnum;

  @Column({ nullable: true, default: null })
  referral_id: number;

  @OneToMany(() => payments, (payment) => payment.discount)
  payments: payments[];

  @AfterLoad()
  convertExpireDateToUTC() {
    if (this.expire_date) {
      this.expire_date = new Date(
        this.expire_date.getTime() -
          this.expire_date.getTimezoneOffset() * 60000,
      );
    }
  }
}
