import { Column, <PERSON>tity, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { PaymentStateEnum } from '../../enums/purchase-state.enum';
import { users } from './users.entity';
import { products } from './product.entity';
import { discounts } from './discounts.entity';
import { purchases } from './purchase.entity';

@Entity()
export class payments extends AbstractEntity {
  @Column()
  order_id: string;

  @ManyToOne(() => users, (user) => user.payments)
  @JoinColumn({ name: 'user_id' })
  user: users;

  @Column()
  user_id: number;

  @ManyToOne(() => products, (product) => product.payments)
  @JoinColumn({ name: 'product_id' })
  product: products;

  @Column()
  product_id: number;

  @Column()
  state: PaymentStateEnum;

  @Column()
  price: number;

  @ManyToOne(() => discounts, (discount) => discount.payments)
  @JoinColumn({ name: 'discount_id' })
  discount: discounts;

  @Column({ nullable: true })
  discount_id: number;

  @Column()
  final_price: number;

  @OneToMany(() => purchases, (purchase) => purchase.payment)
  purchases: purchases[];
}
