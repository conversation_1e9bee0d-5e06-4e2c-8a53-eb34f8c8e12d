import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateDiscountsTable1725517086428 implements MigrationInterface {
  name = 'UpdateDiscountsTable1725517086428';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type for discount type
    await queryRunner.query(
      `CREATE TYPE "discount_type_enum" AS ENUM('simple', 'referral')`,
    );

    // Add type column with enum and default value
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD COLUMN "type" "discount_type_enum" NOT NULL DEFAULT 'simple'`,
    );

    // Add referral_id column
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD COLUMN "referral_id" integer DEFAULT NULL`,
    );

    // Remove company_name column if it exists
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN IF EXISTS "company_name"`,
    );

    // Remove count column if it exists
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN IF EXISTS "count"`,
    );

    // Remove remain_count column if it exists (since it's not in the current entity)
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN IF EXISTS "remain_count"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Add back company_name column
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD COLUMN "company_name" character varying`,
    );

    // Add back count column
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD COLUMN "count" integer`,
    );

    // Add back remain_count column
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD COLUMN "remain_count" integer`,
    );

    // Remove referral_id column
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN "referral_id"`,
    );

    // Remove type column
    await queryRunner.query(`ALTER TABLE "discounts" DROP COLUMN "type"`);

    // Drop enum type
    await queryRunner.query(`DROP TYPE "discount_type_enum"`);
  }
}
