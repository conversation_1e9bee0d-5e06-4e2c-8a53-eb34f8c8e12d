import { MigrationInterface, QueryRunner } from "typeorm";

export class AlterUserVerifiedBy1723884280654 implements MigrationInterface {
    name = 'AlterUserVerifiedBy1723884280654'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."users_verified_by_enum" AS ENUM('PHONE', 'EMAIL')`);
        await queryRunner.query(`ALTER TABLE "users" ADD "verified_by" "public"."users_verified_by_enum" DEFAULT 'PHONE'`);
        await queryRunner.query(`ALTER TYPE "public"."users_gender_enum" RENAME TO "users_gender_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."users_gender_enum" AS ENUM('MALE', 'FEMALE', 'OTHER')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "gender" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "gender" TYPE "public"."users_gender_enum" USING "gender"::"text"::"public"."users_gender_enum"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "gender" SET DEFAULT 'OTHER'`);
        await queryRunner.query(`DROP TYPE "public"."users_gender_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."users_gender_enum_old" AS ENUM('male', 'female', 'other')`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "gender" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "gender" TYPE "public"."users_gender_enum_old" USING "gender"::"text"::"public"."users_gender_enum_old"`);
        await queryRunner.query(`ALTER TABLE "users" ALTER COLUMN "gender" SET DEFAULT 'other'`);
        await queryRunner.query(`DROP TYPE "public"."users_gender_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."users_gender_enum_old" RENAME TO "users_gender_enum"`);
        await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "verified_by"`);
        await queryRunner.query(`DROP TYPE "public"."users_verified_by_enum"`);
    }

}
